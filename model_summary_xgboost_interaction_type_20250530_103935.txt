=== XGBoost Model Summary for Interaction Type ===
Date and Time: 2025-05-30 10:39:35

=== Model Parameters ===
max_depth: 6
learning_rate: 0.1
n_estimators: 100
verbosity: 0
objective: multi:softprob
booster: gbtree
tree_method: auto
n_jobs: -1
gamma: 0
min_child_weight: 1
max_delta_step: 0
subsample: 1
colsample_bytree: 1
colsample_bylevel: 1
colsample_bynode: 1
reg_alpha: 0
reg_lambda: 1
scale_pos_weight: 1
base_score: 0.5
random_state: 42
num_parallel_tree: 1
importance_type: gain
gpu_id: -1
validate_parameters: True

=== Performance Metrics ===
Accuracy: 0.8203
F1 Score: 0.8177
Training Time: 15.39 seconds

=== Classification Report ===
              precision    recall  f1-score   support

           0       0.80      0.83      0.82       442
           1       0.94      0.85      0.89        20
           2       0.00      0.00      0.00         1
           3       0.86      0.86      0.86        22
           4       1.00      0.56      0.71        18
           5       0.60      0.75      0.67         4
           6       1.00      0.50      0.67         2
           7       0.00      0.00      0.00         1
           8       1.00      1.00      1.00        21
           9       0.50      0.33      0.40         3
          10       1.00      1.00      1.00         2
          11       1.00      0.75      0.86         4
          12       0.90      0.84      0.87        93
          13       1.00      1.00      1.00         1
          19       1.00      0.91      0.95        58
          20       1.00      0.99      1.00       109
          21       0.81      0.81      0.81        16
          22       0.88      0.95      0.91        38
          23       0.77      0.67      0.71       581
          25       1.00      1.00      1.00         4
          26       1.00      0.50      0.67         2
          28       1.00      0.60      0.75        10
          30       0.85      0.68      0.75        68
          31       0.84      0.90      0.87       359
          33       0.00      0.00      0.00         1
          35       1.00      1.00      1.00         8
          36       1.00      0.25      0.40         4
          37       0.76      0.82      0.79       767
          38       1.00      0.33      0.50         3
          39       0.00      0.00      0.00         1
          40       1.00      1.00      1.00         1
          41       0.91      0.94      0.92       322
          42       0.78      1.00      0.88        14

    accuracy                           0.82      3000
   macro avg       0.79      0.69      0.72      3000
weighted avg       0.82      0.82      0.82      3000
