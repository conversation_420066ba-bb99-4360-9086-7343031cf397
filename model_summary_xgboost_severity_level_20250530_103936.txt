=== XGBoost Model Summary for Severity Level ===
Date and Time: 2025-05-30 10:39:36

=== Model Parameters ===
max_depth: 6
learning_rate: 0.1
n_estimators: 100
verbosity: 0
objective: multi:softprob
booster: gbtree
tree_method: auto
n_jobs: -1
gamma: 0
min_child_weight: 1
max_delta_step: 0
subsample: 1
colsample_bytree: 1
colsample_bylevel: 1
colsample_bynode: 1
reg_alpha: 0
reg_lambda: 1
scale_pos_weight: 1
base_score: 0.5
random_state: 42
num_parallel_tree: 1
importance_type: gain
gpu_id: -1
validate_parameters: True

=== Performance Metrics ===
Accuracy: 0.8947
F1 Score: 0.8918
Training Time: 1.35 seconds

=== Classification Report ===
              precision    recall  f1-score   support

           0       0.91      0.81      0.86       905
           1       1.00      0.70      0.83       365
           2       0.87      0.98      0.92      1730

    accuracy                           0.89      3000
   macro avg       0.93      0.83      0.87      3000
weighted avg       0.90      0.89      0.89      3000
